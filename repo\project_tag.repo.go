package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ProjectTagOption func(repository.IRepository[models.ProjectTag])

var ProjectTag = func(c core.IContext, options ...ProjectTagOption) repository.IRepository[models.ProjectTag] {
	r := repository.New[models.ProjectTag](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectTagOrderBy(pageOptions *core.PageOptions) ProjectTagOption {
	return func(c repository.IRepository[models.ProjectTag]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}
