package models

import "time"

type Cycle struct {
	BaseModelHardDelete
	CycleStartDate    time.Time `json:"cycle_start_date" gorm:"column:cycle_start_date"`
	CycleEndDate      time.Time `json:"cycle_end_date" gorm:"column:cycle_end_date"`
	Status            string    `json:"status" gorm:"column:status"`
	HourCount         int       `json:"hour_count" gorm:"column:hour_count"`
	TotalCost         float64   `json:"total_cost" gorm:"column:total_cost"`
	TotalOfficialCost float64   `json:"total_official_cost" gorm:"column:total_official_cost"`

	// Relations
	ProjectUsages []ProjectUsage `json:"project_usages,omitempty" gorm:"foreignKey:CycleID"`
	ProjectUsage  *ProjectUsage  `json:"project_usage,omitempty" gorm:"foreignKey:CycleID"`
}

func (Cycle) TableName() string {
	return "cycles"
}
